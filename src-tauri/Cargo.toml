[package]
name = "halloween-quant"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "halloween_quant_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.12.24", features = ["json"] }
tokio = { version = "1.48.0", features = ["full"] }
tokio-macros = { version = "2.5.0" }
pyo3 = { version = "0.27.1", features = ["auto-initialize"] }
anyhow = "1.0.100"
chrono = { version = "0.4.42", features = ["serde"] }
hashbrown = "0.16.0"
async-trait = "0.1.89"
encoding_rs = "0.8.35"
derive-getters = "0.5.0"
csv = "1.4.0"
json5 = "0.4.1"

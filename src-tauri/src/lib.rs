pub mod data_source;
pub mod indicators;

use pyo3::{types::PyAnyMethods, Python};

type AnyResult<T> = Result<T, String>;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> AnyResult<String> {
    let python_version = Python::attach(|py| -> anyhow::Result<String> {
        let sys = py.import("sys")?;
        let version = sys.getattr("version")?.extract()?;
        Ok(version)
    });

    Ok(format!(
        "Your python version is {}",
        python_version.map_err(|e| e.to_string())?
    ))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tokio::test]
async fn test_api() {
    let url = "http://hq.sinajs.cn/list=sz002351,sh600519";
    let response = reqwest::Client::new()
        .get(url)
        .header("Referer", "https://finance.sina.com.cn/")
        .header("host", "hq.sinajs.cn")
        .send()
        .await
        .unwrap();
    println!("{}", response.text().await.unwrap());
}

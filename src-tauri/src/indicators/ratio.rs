use crate::data_source::balance_sheet::BalanceSheet;

pub struct LiquidityRatio<'a> {
    balance_sheet: &'a BalanceSheet,
}

impl<'a> LiquidityRatio<'a> {
    pub fn new(balance_sheet: &'a BalanceSheet) -> Self {
        Self { balance_sheet }
    }

    pub fn current_ratio(&self) -> f32 {
        self.balance_sheet.total_current_assets() / self.balance_sheet.total_liabilities()
    }

    pub fn quick_ratio(&self) -> f32 {
        (self.balance_sheet.total_current_assets() - self.balance_sheet.inventories())
            / self.balance_sheet.total_current_liabilities()
    }

    pub fn cash_ratio(&self) -> f32 {
        self.balance_sheet.cash_and_cash_equivalents()
            / self.balance_sheet.total_current_liabilities()
    }
}

use std::sync::Arc;

use anyhow::Context;
use chrono::{Days, Local};

use crate::data_source::{
    adjusted_share_price::{get_adjusted_share_price, AdjustDirection},
    stock_quote::HistoricStockQuote,
};

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
pub enum ProfitRateCalculationType {
    Logarithm,
    Arithmetic,
}

pub enum MarketIndex {
    SseCompositeIndex, // 上证
    Csi300Index,       // 沪深300
}

impl MarketIndex {
    pub fn get_code(&self) -> &'static str {
        match self {
            Self::SseCompositeIndex => "sh000001",
            Self::Csi300Index => "sh000300",
        }
    }
}

pub fn rolling_profit_rate(price: &[f32], profit_type: ProfitRateCalculationType) -> Vec<f32> {
    match profit_type {
        ProfitRateCalculationType::Arithmetic => {
            price.windows(2).map(|x| (x[1] - x[0]) / x[0]).collect()
        }
        ProfitRateCalculationType::Logarithm => price
            .windows(2)
            .map(|x| f32::ln(x[1] / x[0]))
            .collect::<Vec<_>>(),
    }
}

pub fn calculate_beta(
    share_price: &[f32],
    market_price: &[f32],
    profit_type: ProfitRateCalculationType,
) -> Option<f32> {
    if share_price.len() != market_price.len() {
        return None;
    }

    let share_profit_rate = rolling_profit_rate(share_price, profit_type);
    let market_profit_rate = rolling_profit_rate(market_price, profit_type);
    let n = share_profit_rate.len();

    let avg_share_profit_rate = share_profit_rate.iter().sum::<f32>() / (n as f32);
    let avg_market_profit_rate = market_profit_rate.iter().sum::<f32>() / (n as f32);

    let cov = share_profit_rate
        .iter()
        .zip(market_profit_rate.iter())
        .map(|(rs, rm)| (rs - avg_share_profit_rate) * (rm - avg_market_profit_rate))
        .sum::<f32>()
        / (n as f32 - 1.0);

    let var_market = market_profit_rate
        .iter()
        .map(|x| (x - avg_market_profit_rate).powi(2))
        .sum::<f32>()
        / (n as f32 - 1.0);

    Some(cov / var_market)
}

pub async fn beta_from_request(
    code: impl AsRef<str>,
    profit_type: ProfitRateCalculationType,
    market: MarketIndex,
    window_days: u64,
) -> anyhow::Result<f32> {
    let market_code = market.get_code();
    let now = Local::now().naive_local().date();
    let start_date = now - Days::new(window_days);

    let code = Arc::new(code.as_ref().to_owned());
    let market_code = Arc::new(market_code.to_owned());
    let forward_adj_share_prices = tokio::spawn(async move {
        get_adjusted_share_price(code.as_ref(), AdjustDirection::Forward).await
    });
    let market_prices = tokio::spawn(async move {
        HistoricStockQuote::from_api(market_code.as_ref(), 240, window_days).await
    });

    let forward_adj_share_prices = forward_adj_share_prices
        .await??
        .into_iter()
        .filter(|x| x.date() >= &start_date)
        .collect::<Vec<_>>();

    let market_prices = market_prices
        .await??
        .into_iter()
        .filter(|x| x.day() >= &start_date)
        .collect::<Vec<_>>();

    let mut ptr_m: usize = 0;
    let mut ptr_f: usize = 0;

    let mut market_prices_f32: Vec<f32> = Vec::with_capacity(market_prices.len());
    let mut forward_adj_share_prices_f32: Vec<f32> =
        Vec::with_capacity(forward_adj_share_prices.len());

    while ptr_m < market_prices.len() && ptr_f < forward_adj_share_prices.len() {
        let m = &market_prices[ptr_m];
        let f = &forward_adj_share_prices[ptr_f];
        if m.day() == f.date() {
            market_prices_f32.push(*m.close());
            forward_adj_share_prices_f32.push(*f.price());
            ptr_f += 1;
            ptr_m += 1
        } else if m.day() > f.date() {
            ptr_f += 1;
        } else {
            ptr_m += 1;
        }
    }

    let beta = calculate_beta(
        &forward_adj_share_prices_f32,
        &market_prices_f32,
        profit_type,
    )
    .context("failed to get beta result")?;
    Ok(beta)
}

#[tokio::test]
async fn test_beta() {
    let beta = beta_from_request(
        "002456",
        ProfitRateCalculationType::Logarithm,
        MarketIndex::Csi300Index,
        365,
    )
    .await
    .unwrap();
    println!("{}", beta);
}

#[test]
fn t() {
    let now = Local::now().naive_local().date();
    println!("{}", now - Days::new(365));
}

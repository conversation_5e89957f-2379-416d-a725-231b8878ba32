use hashbrown::HashMap;
use std::sync::LazyLock;

static INDEX_LIST: LazyLock<HashMap<&str, &str>> = LazyLock::new(|| {
    HashMap::from_iter([
        ("sh", "sh000001"),
        ("sz", "sz399001"),
        ("hs300", "sh000300"),
        ("sz50", "sh000016"),
        ("zxb", "sz399005"),
        ("cyb", "sz399006"),
        ("zx300", "sz399008"),
        ("zh500", "sh000905"),
    ])
});

pub fn code_to_symbol(code: &str) -> Option<String> {
    if code.len() != 6 {
        return Some(code.to_owned());
    }
    let chars = code.chars();
    let char_vec = chars.take(1).collect::<Vec<_>>();
    let first = char_vec.first()?;
    let first_two = code.chars().take(2).collect::<String>();
    match INDEX_LIST.get(code) {
        Some(&v) => Some(v.to_owned()),
        None => {
            if ['5', '6', '9'].contains(first) || ["11", "13"].contains(&first_two.as_str()) {
                Some(format!("sh{}", code))
            } else {
                Some(format!("sz{}", code))
            }
        }
    }
}

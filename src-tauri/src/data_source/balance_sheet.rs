use super::field_value::FieldValue;
use crate::indicators::ratio::LiquidityRatio;
use anyhow::Context;
use derive_getters::Getters;
use serde::Deserialize;

fn parse_line(line: &str) -> Option<(&str, Vec<&str>)> {
    let mut fields = line.trim().split('\t').into_iter();
    let header = fields.next()?;
    let fields = fields.collect::<Vec<_>>();

    Some((header.trim(), fields))
}

#[derive(Debug, Clone, Deserialize, Getters)]
pub struct BalanceSheet {
    report_date: String, // 报表日期
    unit: String,        // 单位

    // 流动资产
    cash_and_cash_equivalents: f32,             // 货币资金
    trading_financial_assets: f32,              // 交易性金融资产
    derivative_financial_assets: f32,           // 衍生金融资产
    notes_and_accounts_receivable: f32,         // 应收票据及应收账款
    notes_receivable: f32,                      // 应收票据
    accounts_receivable: f32,                   // 应收账款
    accounts_financing_receivable: f32,         // 应收款项融资
    prepayments: f32,                           // 预付款项
    other_receivables_total: f32,               // 其他应收款(合计)
    interest_receivable: f32,                   // 应收利息
    dividends_receivable: f32,                  // 应收股利
    other_receivables: f32,                     // 其他应收款
    repurchase_financial_assets: f32,           // 买入返售金融资产
    inventories: f32,                           // 存货
    assets_held_for_sale: f32,                  // 划分为持有待售的资产
    noncurrent_assets_due_within_one_year: f32, // 一年内到期的非流动资产
    prepaid_expenses: f32,                      // 待摊费用
    pending_current_asset_gains_or_losses: f32, // 待处理流动资产损益
    other_current_assets: f32,                  // 其他流动资产
    total_current_assets: f32,                  // 流动资产合计

    loans_and_advances: f32,                  // 发放贷款及垫款
    available_for_sale_financial_assets: f32, // 可供出售金融资产
    held_to_maturity_investments: f32,        // 持有至到期投资
    long_term_receivables: f32,               // 长期应收款
    long_term_equity_investments: f32,        // 长期股权投资
    investment_properties: f32,               // 投资性房地产
    construction_in_progress_total: f32,      // 在建工程(合计)
    construction_in_progress: f32,            // 在建工程
    engineering_materials: f32,               // 工程物资
    fixed_assets_and_disposal_total: f32,     // 固定资产及清理(合计)
    net_fixed_assets: f32,                    // 固定资产净额
    fixed_assets_disposal: f32,               // 固定资产清理
    productive_biological_assets: f32,        // 生产性生物资产
    non_profit_biological_assets: f32,        // 公益性生物资产
    oil_and_gas_assets: f32,                  // 油气资产
    right_of_use_assets: f32,                 // 使用权资产
    intangible_assets: f32,                   // 无形资产
    development_expenditure: f32,             // 开发支出
    goodwill: f32,                            // 商誉
    long_term_prepaid_expenses: f32,          // 长期待摊费用
    deferred_income_tax_assets: f32,          // 递延所得税资产
    other_noncurrent_assets: f32,             // 其他非流动资产
    total_noncurrent_assets: f32,             // 非流动资产合计
    total_assets: f32,                        // 资产总计

    short_term_loans: f32,                           // 短期借款
    trading_financial_liabilities: f32,              // 交易性金融负债
    notes_and_accounts_payable: f32,                 // 应付票据及应付账款
    notes_payable: f32,                              // 应付票据
    accounts_payable: f32,                           // 应付账款
    advance_receipts: f32,                           // 预收款项
    commission_payable: f32,                         // 应付手续费及佣金
    employee_payables: f32,                          // 应付职工薪酬
    taxes_payable: f32,                              // 应交税费
    other_payables_total: f32,                       // 其他应付款(合计)
    interest_payable: f32,                           // 应付利息
    dividends_payable: f32,                          // 应付股利
    other_payables: f32,                             // 其他应付款
    accrued_expenses: f32,                           // 预提费用
    deferred_income_within_one_year: f32,            // 一年内的递延收益
    short_term_bonds_payable: f32,                   // 应付短期债券
    noncurrent_liabilities_due_within_one_year: f32, // 一年内到期的非流动负债
    other_current_liabilities: f32,                  // 其他流动负债
    total_current_liabilities: f32,                  // 流动负债合计

    long_term_loans: f32,                  // 长期借款
    bonds_payable: f32,                    // 应付债券
    lease_liabilities: f32,                // 租赁负债
    long_term_employee_payables: f32,      // 长期应付职工薪酬
    long_term_payables_total: f32,         // 长期应付款(合计)
    long_term_payables: f32,               // 长期应付款
    special_payables: f32,                 // 专项应付款
    estimated_noncurrent_liabilities: f32, // 预计非流动负债
    deferred_income_tax_liabilities: f32,  // 递延所得税负债
    long_term_deferred_income: f32,        // 长期递延收益
    other_noncurrent_liabilities: f32,     // 其他非流动负债
    total_noncurrent_liabilities: f32,     // 非流动负债合计
    total_liabilities: f32,                // 负债合计

    paid_in_capital: f32,                     // 实收资本(或股本)
    capital_reserve: f32,                     // 资本公积
    treasury_stock: f32,                      // 减：库存股
    other_comprehensive_income: f32,          // 其他综合收益
    special_reserve: f32,                     // 专项储备
    surplus_reserve: f32,                     // 盈余公积
    general_risk_reserve: f32,                // 一般风险准备
    undistributed_profits: f32,               // 未分配利润
    total_equity_attributable_to_parent: f32, // 归属于母公司股东权益合计
    minority_interest: f32,                   // 少数股东权益
    total_equity: f32,                        // 所有者权益(或股东权益)合计
    total_liabilities_and_equity: f32,        // 负债和所有者权益(或股东权益)总计
}

impl BalanceSheet {
    pub fn liquidity(&self) -> LiquidityRatio<'_> {
        LiquidityRatio::new(&self)
    }

    pub fn get(&self, field: impl AsRef<str>) -> FieldValue<'_> {
        match field.as_ref() {
            "报表日期" => FieldValue::Str(&self.report_date),
            "单位" => FieldValue::Str(&self.unit),
            "货币资金" => FieldValue::Float(self.cash_and_cash_equivalents),
            "交易性金融资产" => FieldValue::Float(self.trading_financial_assets),
            "衍生金融资产" => FieldValue::Float(self.derivative_financial_assets),
            "应收票据及应收账款" => FieldValue::Float(self.notes_and_accounts_receivable),
            "应收票据" => FieldValue::Float(self.notes_receivable),
            "应收账款" => FieldValue::Float(self.accounts_receivable),
            "应收款项融资" => FieldValue::Float(self.accounts_financing_receivable),
            "预付款项" => FieldValue::Float(self.prepayments),
            "其他应收款(合计)" => FieldValue::Float(self.other_receivables_total),
            "应收利息" => FieldValue::Float(self.interest_receivable),
            "应收股利" => FieldValue::Float(self.dividends_receivable),
            "其他应收款" => FieldValue::Float(self.other_receivables),
            "买入返售金融资产" => FieldValue::Float(self.repurchase_financial_assets),
            "存货" => FieldValue::Float(self.inventories),
            "划分为持有待售的资产" => FieldValue::Float(self.assets_held_for_sale),
            "一年内到期的非流动资产" => {
                FieldValue::Float(self.noncurrent_assets_due_within_one_year)
            }
            "待摊费用" => FieldValue::Float(self.prepaid_expenses),
            "待处理流动资产损益" => {
                FieldValue::Float(self.pending_current_asset_gains_or_losses)
            }
            "其他流动资产" => FieldValue::Float(self.other_current_assets),
            "流动资产合计" => FieldValue::Float(self.total_current_assets),
            "发放贷款及垫款" => FieldValue::Float(self.loans_and_advances),
            "可供出售金融资产" => {
                FieldValue::Float(self.available_for_sale_financial_assets)
            }
            "持有至到期投资" => FieldValue::Float(self.held_to_maturity_investments),
            "长期应收款" => FieldValue::Float(self.long_term_receivables),
            "长期股权投资" => FieldValue::Float(self.long_term_equity_investments),
            "投资性房地产" => FieldValue::Float(self.investment_properties),
            "在建工程(合计)" => FieldValue::Float(self.construction_in_progress_total),
            "在建工程" => FieldValue::Float(self.construction_in_progress),
            "工程物资" => FieldValue::Float(self.engineering_materials),
            "固定资产及清理(合计)" => {
                FieldValue::Float(self.fixed_assets_and_disposal_total)
            }
            "固定资产净额" => FieldValue::Float(self.net_fixed_assets),
            "固定资产清理" => FieldValue::Float(self.fixed_assets_disposal),
            "生产性生物资产" => FieldValue::Float(self.productive_biological_assets),
            "公益性生物资产" => FieldValue::Float(self.non_profit_biological_assets),
            "油气资产" => FieldValue::Float(self.oil_and_gas_assets),
            "使用权资产" => FieldValue::Float(self.right_of_use_assets),
            "无形资产" => FieldValue::Float(self.intangible_assets),
            "开发支出" => FieldValue::Float(self.development_expenditure),
            "商誉" => FieldValue::Float(self.goodwill),
            "长期待摊费用" => FieldValue::Float(self.long_term_prepaid_expenses),
            "递延所得税资产" => FieldValue::Float(self.deferred_income_tax_assets),
            "其他非流动资产" => FieldValue::Float(self.other_noncurrent_assets),
            "非流动资产合计" => FieldValue::Float(self.total_noncurrent_assets),
            "资产总计" => FieldValue::Float(self.total_assets),
            "短期借款" => FieldValue::Float(self.short_term_loans),
            "交易性金融负债" => FieldValue::Float(self.trading_financial_liabilities),
            "应付票据及应付账款" => FieldValue::Float(self.notes_and_accounts_payable),
            "应付票据" => FieldValue::Float(self.notes_payable),
            "应付账款" => FieldValue::Float(self.accounts_payable),
            "预收款项" => FieldValue::Float(self.advance_receipts),
            "应付手续费及佣金" => FieldValue::Float(self.commission_payable),
            "应付职工薪酬" => FieldValue::Float(self.employee_payables),
            "应交税费" => FieldValue::Float(self.taxes_payable),
            "其他应付款(合计)" => FieldValue::Float(self.other_payables_total),
            "应付利息" => FieldValue::Float(self.interest_payable),
            "应付股利" => FieldValue::Float(self.dividends_payable),
            "其他应付款" => FieldValue::Float(self.other_payables),
            "预提费用" => FieldValue::Float(self.accrued_expenses),
            "一年内的递延收益" => FieldValue::Float(self.deferred_income_within_one_year),
            "应付短期债券" => FieldValue::Float(self.short_term_bonds_payable),
            "一年内到期的非流动负债" => {
                FieldValue::Float(self.noncurrent_liabilities_due_within_one_year)
            }
            "其他流动负债" => FieldValue::Float(self.other_current_liabilities),
            "流动负债合计" => FieldValue::Float(self.total_current_liabilities),
            "长期借款" => FieldValue::Float(self.long_term_loans),
            "应付债券" => FieldValue::Float(self.bonds_payable),
            "租赁负债" => FieldValue::Float(self.lease_liabilities),
            "长期应付职工薪酬" => FieldValue::Float(self.long_term_employee_payables),
            "长期应付款(合计)" => FieldValue::Float(self.long_term_payables_total),
            "长期应付款" => FieldValue::Float(self.long_term_payables),
            "专项应付款" => FieldValue::Float(self.special_payables),
            "预计非流动负债" => FieldValue::Float(self.estimated_noncurrent_liabilities),
            "递延所得税负债" => FieldValue::Float(self.deferred_income_tax_liabilities),
            "长期递延收益" => FieldValue::Float(self.long_term_deferred_income),
            "其他非流动负债" => FieldValue::Float(self.other_noncurrent_liabilities),
            "非流动负债合计" => FieldValue::Float(self.total_noncurrent_liabilities),
            "负债合计" => FieldValue::Float(self.total_liabilities),
            "实收资本(或股本)" => FieldValue::Float(self.paid_in_capital),
            "资本公积" => FieldValue::Float(self.capital_reserve),
            "减：库存股" => FieldValue::Float(self.treasury_stock),
            "其他综合收益" => FieldValue::Float(self.other_comprehensive_income),
            "专项储备" => FieldValue::Float(self.special_reserve),
            "盈余公积" => FieldValue::Float(self.surplus_reserve),
            "一般风险准备" => FieldValue::Float(self.general_risk_reserve),
            "未分配利润" => FieldValue::Float(self.undistributed_profits),
            "归属于母公司股东权益合计" => {
                FieldValue::Float(self.total_equity_attributable_to_parent)
            }
            "少数股东权益" => FieldValue::Float(self.minority_interest),
            "所有者权益(或股东权益)合计" => FieldValue::Float(self.total_equity),
            "负债和所有者权益(或股东权益)总计" => {
                FieldValue::Float(self.total_liabilities_and_equity)
            }
            _ => FieldValue::None,
        }
    }

    pub fn headers() -> &'static [&'static str] {
        &[
            "货币资金",
            "交易性金融资产",
            "衍生金融资产",
            "应收票据及应收账款",
            "应收票据",
            "应收账款",
            "应收款项融资",
            "预付款项",
            "其他应收款(合计)",
            "应收利息",
            "应收股利",
            "其他应收款",
            "买入返售金融资产",
            "存货",
            "划分为持有待售的资产",
            "一年内到期的非流动资产",
            "待摊费用",
            "待处理流动资产损益",
            "其他流动资产",
            "流动资产合计",
            "发放贷款及垫款",
            "可供出售金融资产",
            "持有至到期投资",
            "长期应收款",
            "长期股权投资",
            "投资性房地产",
            "在建工程(合计)",
            "在建工程",
            "工程物资",
            "固定资产及清理(合计)",
            "固定资产净额",
            "固定资产清理",
            "生产性生物资产",
            "公益性生物资产",
            "油气资产",
            "使用权资产",
            "无形资产",
            "开发支出",
            "商誉",
            "长期待摊费用",
            "递延所得税资产",
            "其他非流动资产",
            "非流动资产合计",
            "资产总计",
            "短期借款",
            "交易性金融负债",
            "应付票据及应付账款",
            "应付票据",
            "应付账款",
            "预收款项",
            "应付手续费及佣金",
            "应付职工薪酬",
            "应交税费",
            "其他应付款(合计)",
            "应付利息",
            "应付股利",
            "其他应付款",
            "预提费用",
            "一年内的递延收益",
            "应付短期债券",
            "一年内到期的非流动负债",
            "其他流动负债",
            "流动负债合计",
            "长期借款",
            "应付债券",
            "租赁负债",
            "长期应付职工薪酬",
            "长期应付款(合计)",
            "长期应付款",
            "专项应付款",
            "预计非流动负债",
            "递延所得税负债",
            "长期递延收益",
            "其他非流动负债",
            "非流动负债合计",
            "负债合计",
            "实收资本(或股本)",
            "资本公积",
            "减：库存股",
            "其他综合收益",
            "专项储备",
            "盈余公积",
            "一般风险准备",
            "未分配利润",
            "归属于母公司股东权益合计",
            "少数股东权益",
            "所有者权益(或股东权益)合计",
            "负债和所有者权益(或股东权益)总计",
        ]
    }

    pub async fn from_api(code: impl AsRef<str>) -> anyhow::Result<Vec<Self>> {
        let url = format!("http://money.finance.sina.com.cn/corp/go.php/vDOWN_BalanceSheet/displaytype/4/stockid/{}/ctrl/all.phtml", code.as_ref());
        let response = reqwest::Client::new()
            .get(url)
            .send()
            .await?
            .bytes()
            .await?;

        let (text, _, _) = encoding_rs::GBK.decode(&response);
        let lines = text.lines();
        let mut report_date: Vec<String> = vec![];
        let mut unit: Vec<String> = vec![];
        let mut cash_and_cash_equivalents: Vec<f32> = vec![];
        let mut trading_financial_assets: Vec<f32> = vec![];
        let mut derivative_financial_assets: Vec<f32> = vec![];
        let mut notes_and_accounts_receivable: Vec<f32> = vec![];
        let mut notes_receivable: Vec<f32> = vec![];
        let mut accounts_receivable: Vec<f32> = vec![];
        let mut accounts_financing_receivable: Vec<f32> = vec![];
        let mut prepayments: Vec<f32> = vec![];
        let mut other_receivables_total: Vec<f32> = vec![];
        let mut interest_receivable: Vec<f32> = vec![];
        let mut dividends_receivable: Vec<f32> = vec![];
        let mut other_receivables: Vec<f32> = vec![];
        let mut repurchase_financial_assets: Vec<f32> = vec![];
        let mut inventories: Vec<f32> = vec![];
        let mut assets_held_for_sale: Vec<f32> = vec![];
        let mut noncurrent_assets_due_within_one_year: Vec<f32> = vec![];
        let mut prepaid_expenses: Vec<f32> = vec![];
        let mut pending_current_asset_gains_or_losses: Vec<f32> = vec![];
        let mut other_current_assets: Vec<f32> = vec![];
        let mut total_current_assets: Vec<f32> = vec![];
        let mut loans_and_advances: Vec<f32> = vec![];
        let mut available_for_sale_financial_assets: Vec<f32> = vec![];
        let mut held_to_maturity_investments: Vec<f32> = vec![];
        let mut long_term_receivables: Vec<f32> = vec![];
        let mut long_term_equity_investments: Vec<f32> = vec![];
        let mut investment_properties: Vec<f32> = vec![];
        let mut construction_in_progress_total: Vec<f32> = vec![];
        let mut construction_in_progress: Vec<f32> = vec![];
        let mut engineering_materials: Vec<f32> = vec![];
        let mut fixed_assets_and_disposal_total: Vec<f32> = vec![];
        let mut net_fixed_assets: Vec<f32> = vec![];
        let mut fixed_assets_disposal: Vec<f32> = vec![];
        let mut productive_biological_assets: Vec<f32> = vec![];
        let mut non_profit_biological_assets: Vec<f32> = vec![];
        let mut oil_and_gas_assets: Vec<f32> = vec![];
        let mut right_of_use_assets: Vec<f32> = vec![];
        let mut intangible_assets: Vec<f32> = vec![];
        let mut development_expenditure: Vec<f32> = vec![];
        let mut goodwill: Vec<f32> = vec![];
        let mut long_term_prepaid_expenses: Vec<f32> = vec![];
        let mut deferred_income_tax_assets: Vec<f32> = vec![];
        let mut other_noncurrent_assets: Vec<f32> = vec![];
        let mut total_noncurrent_assets: Vec<f32> = vec![];
        let mut total_assets: Vec<f32> = vec![];
        let mut short_term_loans: Vec<f32> = vec![];
        let mut trading_financial_liabilities: Vec<f32> = vec![];
        let mut notes_and_accounts_payable: Vec<f32> = vec![];
        let mut notes_payable: Vec<f32> = vec![];
        let mut accounts_payable: Vec<f32> = vec![];
        let mut advance_receipts: Vec<f32> = vec![];
        let mut commission_payable: Vec<f32> = vec![];
        let mut employee_payables: Vec<f32> = vec![];
        let mut taxes_payable: Vec<f32> = vec![];
        let mut other_payables_total: Vec<f32> = vec![];
        let mut interest_payable: Vec<f32> = vec![];
        let mut dividends_payable: Vec<f32> = vec![];
        let mut other_payables: Vec<f32> = vec![];
        let mut accrued_expenses: Vec<f32> = vec![];
        let mut deferred_income_within_one_year: Vec<f32> = vec![];
        let mut short_term_bonds_payable: Vec<f32> = vec![];
        let mut noncurrent_liabilities_due_within_one_year: Vec<f32> = vec![];
        let mut other_current_liabilities: Vec<f32> = vec![];
        let mut total_current_liabilities: Vec<f32> = vec![];
        let mut long_term_loans: Vec<f32> = vec![];
        let mut bonds_payable: Vec<f32> = vec![];
        let mut lease_liabilities: Vec<f32> = vec![];
        let mut long_term_employee_payables: Vec<f32> = vec![];
        let mut long_term_payables_total: Vec<f32> = vec![];
        let mut long_term_payables: Vec<f32> = vec![];
        let mut special_payables: Vec<f32> = vec![];
        let mut estimated_noncurrent_liabilities: Vec<f32> = vec![];
        let mut deferred_income_tax_liabilities: Vec<f32> = vec![];
        let mut long_term_deferred_income: Vec<f32> = vec![];
        let mut other_noncurrent_liabilities: Vec<f32> = vec![];
        let mut total_noncurrent_liabilities: Vec<f32> = vec![];
        let mut total_liabilities: Vec<f32> = vec![];
        let mut paid_in_capital: Vec<f32> = vec![];
        let mut capital_reserve: Vec<f32> = vec![];
        let mut treasury_stock: Vec<f32> = vec![];
        let mut other_comprehensive_income: Vec<f32> = vec![];
        let mut special_reserve: Vec<f32> = vec![];
        let mut surplus_reserve: Vec<f32> = vec![];
        let mut general_risk_reserve: Vec<f32> = vec![];
        let mut undistributed_profits: Vec<f32> = vec![];
        let mut total_equity_attributable_to_parent: Vec<f32> = vec![];
        let mut minority_interest: Vec<f32> = vec![];
        let mut total_equity: Vec<f32> = vec![];
        let mut total_liabilities_and_equity: Vec<f32> = vec![];

        for line in lines {
            let (header, fields) = parse_line(line).context("failed to parse api data")?;
            match header {
                "报表日期" => report_date = fields.into_iter().map(|x| x.to_owned()).collect(),
                "单位" => unit = fields.into_iter().map(|x| x.to_owned()).collect(),
                "货币资金" => {
                    cash_and_cash_equivalents = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field cash_and_cash_equivalents")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "交易性金融资产" => {
                    trading_financial_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field trading_financial_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "衍生金融资产" => {
                    derivative_financial_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field derivative_financial_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应收票据及应收账款" => {
                    notes_and_accounts_receivable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field notes_and_accounts_receivable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应收票据" => {
                    notes_receivable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field notes_receivable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应收账款" => {
                    accounts_receivable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field accounts_receivable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应收款项融资" => {
                    accounts_financing_receivable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field accounts_financing_receivable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "预付款项" => {
                    prepayments = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field prepayments")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他应收款(合计)" => {
                    other_receivables_total = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_receivables_total")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应收利息" => {
                    interest_receivable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field interest_receivable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应收股利" => {
                    dividends_receivable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field dividends_receivable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他应收款" => {
                    other_receivables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_receivables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "买入返售金融资产" => {
                    repurchase_financial_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field repurchase_financial_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "存货" => {
                    inventories = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field inventories")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "划分为持有待售的资产" => {
                    assets_held_for_sale = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field assets_held_for_sale")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "一年内到期的非流动资产" => {
                    noncurrent_assets_due_within_one_year = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>().context(
                                "failed to parse field noncurrent_assets_due_within_one_year",
                            )
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "待摊费用" => {
                    prepaid_expenses = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field prepaid_expenses")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "待处理流动资产损益" => {
                    pending_current_asset_gains_or_losses = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>().context(
                                "failed to parse field pending_current_asset_gains_or_losses",
                            )
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他流动资产" => {
                    other_current_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_current_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "流动资产合计" => {
                    total_current_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_current_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "发放贷款及垫款" => {
                    loans_and_advances = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field loans_and_advances")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "可供出售金融资产" => {
                    available_for_sale_financial_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>().context(
                                "failed to parse field available_for_sale_financial_assets",
                            )
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "持有至到期投资" => {
                    held_to_maturity_investments = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field held_to_maturity_investments")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期应收款" => {
                    long_term_receivables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_receivables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期股权投资" => {
                    long_term_equity_investments = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_equity_investments")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "投资性房地产" => {
                    investment_properties = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field investment_properties")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "在建工程(合计)" => {
                    construction_in_progress_total = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field construction_in_progress_total")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "在建工程" => {
                    construction_in_progress = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field construction_in_progress")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "工程物资" => {
                    engineering_materials = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field engineering_materials")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "固定资产及清理(合计)" => {
                    fixed_assets_and_disposal_total = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field fixed_assets_and_disposal_total")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "固定资产净额" => {
                    net_fixed_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field net_fixed_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "固定资产清理" => {
                    fixed_assets_disposal = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field fixed_assets_disposal")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "生产性生物资产" => {
                    productive_biological_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field productive_biological_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "公益性生物资产" => {
                    non_profit_biological_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field non_profit_biological_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "油气资产" => {
                    oil_and_gas_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field oil_and_gas_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "使用权资产" => {
                    right_of_use_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field right_of_use_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "无形资产" => {
                    intangible_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field intangible_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "开发支出" => {
                    development_expenditure = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field development_expenditure")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "商誉" => {
                    goodwill = fields
                        .into_iter()
                        .map(|x| x.parse::<f32>().context("failed to parse field goodwill"))
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期待摊费用" => {
                    long_term_prepaid_expenses = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_prepaid_expenses")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "递延所得税资产" => {
                    deferred_income_tax_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field deferred_income_tax_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他非流动资产" => {
                    other_noncurrent_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_noncurrent_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "非流动资产合计" => {
                    total_noncurrent_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_noncurrent_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "资产总计" => {
                    total_assets = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_assets")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "短期借款" => {
                    short_term_loans = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field short_term_loans")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "交易性金融负债" => {
                    trading_financial_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field trading_financial_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付票据及应付账款" => {
                    notes_and_accounts_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field notes_and_accounts_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付票据" => {
                    notes_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field notes_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付账款" => {
                    accounts_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field accounts_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "预收款项" => {
                    advance_receipts = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field advance_receipts")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付手续费及佣金" => {
                    commission_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field commission_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付职工薪酬" => {
                    employee_payables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field employee_payables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应交税费" => {
                    taxes_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field taxes_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他应付款(合计)" => {
                    other_payables_total = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_payables_total")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付利息" => {
                    interest_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field interest_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付股利" => {
                    dividends_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field dividends_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他应付款" => {
                    other_payables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_payables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "预提费用" => {
                    accrued_expenses = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field accrued_expenses")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "一年内的递延收益" => {
                    deferred_income_within_one_year = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field deferred_income_within_one_year")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付短期债券" => {
                    short_term_bonds_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field short_term_bonds_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "一年内到期的非流动负债" => {
                    noncurrent_liabilities_due_within_one_year = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>().context(
                                "failed to parse field noncurrent_liabilities_due_within_one_year",
                            )
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他流动负债" => {
                    other_current_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_current_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "流动负债合计" => {
                    total_current_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_current_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期借款" => {
                    long_term_loans = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_loans")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "应付债券" => {
                    bonds_payable = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field bonds_payable")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "租赁负债" => {
                    lease_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field lease_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期应付职工薪酬" => {
                    long_term_employee_payables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_employee_payables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期应付款(合计)" => {
                    long_term_payables_total = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_payables_total")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期应付款" => {
                    long_term_payables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_payables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "专项应付款" => {
                    special_payables = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field special_payables")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "预计非流动负债" => {
                    estimated_noncurrent_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field estimated_noncurrent_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "递延所得税负债" => {
                    deferred_income_tax_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field deferred_income_tax_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "长期递延收益" => {
                    long_term_deferred_income = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field long_term_deferred_income")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他非流动负债" => {
                    other_noncurrent_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_noncurrent_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "非流动负债合计" => {
                    total_noncurrent_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_noncurrent_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "负债合计" => {
                    total_liabilities = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_liabilities")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "实收资本(或股本)" => {
                    paid_in_capital = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field paid_in_capital")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "资本公积" => {
                    capital_reserve = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field capital_reserve")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "减：库存股" => {
                    treasury_stock = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field treasury_stock")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "其他综合收益" => {
                    other_comprehensive_income = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field other_comprehensive_income")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "专项储备" => {
                    special_reserve = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field special_reserve")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "盈余公积" => {
                    surplus_reserve = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field surplus_reserve")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "一般风险准备" => {
                    general_risk_reserve = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field general_risk_reserve")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "未分配利润" => {
                    undistributed_profits = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field undistributed_profits")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "归属于母公司股东权益合计" => {
                    total_equity_attributable_to_parent = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>().context(
                                "failed to parse field total_equity_attributable_to_parent",
                            )
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "少数股东权益" => {
                    minority_interest = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field minority_interest")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "所有者权益(或股东权益)合计" => {
                    total_equity = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_equity")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                "负债和所有者权益(或股东权益)总计" => {
                    total_liabilities_and_equity = fields
                        .into_iter()
                        .map(|x| {
                            x.parse::<f32>()
                                .context("failed to parse field total_liabilities_and_equity")
                        })
                        .collect::<anyhow::Result<Vec<f32>>>()?
                }
                _ => continue,
            }
        }
        let total = report_date.len();
        let mut result = Vec::with_capacity(total);
        for i in 0..total {
            let rp_date = report_date[i].as_str();
            if rp_date == "********" {
                continue;
            }
            let sheet = Self {
                report_date: rp_date.to_owned(),
                unit: unit[i].clone(),
                cash_and_cash_equivalents: cash_and_cash_equivalents[i],
                trading_financial_assets: trading_financial_assets[i],
                derivative_financial_assets: derivative_financial_assets[i],
                notes_and_accounts_receivable: notes_and_accounts_receivable[i],
                notes_receivable: notes_receivable[i],
                accounts_receivable: accounts_receivable[i],
                accounts_financing_receivable: accounts_financing_receivable[i],
                prepayments: prepayments[i],
                other_receivables_total: other_receivables_total[i],
                interest_receivable: interest_receivable[i],
                dividends_receivable: dividends_receivable[i],
                other_receivables: other_receivables[i],
                repurchase_financial_assets: repurchase_financial_assets[i],
                inventories: inventories[i],
                assets_held_for_sale: assets_held_for_sale[i],
                noncurrent_assets_due_within_one_year: noncurrent_assets_due_within_one_year[i],
                prepaid_expenses: prepaid_expenses[i],
                pending_current_asset_gains_or_losses: pending_current_asset_gains_or_losses[i],
                other_current_assets: other_current_assets[i],
                total_current_assets: total_current_assets[i],
                loans_and_advances: loans_and_advances[i],
                available_for_sale_financial_assets: available_for_sale_financial_assets[i],
                held_to_maturity_investments: held_to_maturity_investments[i],
                long_term_receivables: long_term_receivables[i],
                long_term_equity_investments: long_term_equity_investments[i],
                investment_properties: investment_properties[i],
                construction_in_progress_total: construction_in_progress_total[i],
                construction_in_progress: construction_in_progress[i],
                engineering_materials: engineering_materials[i],
                fixed_assets_and_disposal_total: fixed_assets_and_disposal_total[i],
                net_fixed_assets: net_fixed_assets[i],
                fixed_assets_disposal: fixed_assets_disposal[i],
                productive_biological_assets: productive_biological_assets[i],
                non_profit_biological_assets: non_profit_biological_assets[i],
                oil_and_gas_assets: oil_and_gas_assets[i],
                right_of_use_assets: right_of_use_assets[i],
                intangible_assets: intangible_assets[i],
                development_expenditure: development_expenditure[i],
                goodwill: goodwill[i],
                long_term_prepaid_expenses: long_term_prepaid_expenses[i],
                deferred_income_tax_assets: deferred_income_tax_assets[i],
                other_noncurrent_assets: other_noncurrent_assets[i],
                total_noncurrent_assets: total_noncurrent_assets[i],
                total_assets: total_assets[i],
                short_term_loans: short_term_loans[i],
                trading_financial_liabilities: trading_financial_liabilities[i],
                notes_and_accounts_payable: notes_and_accounts_payable[i],
                notes_payable: notes_payable[i],
                accounts_payable: accounts_payable[i],
                advance_receipts: advance_receipts[i],
                commission_payable: commission_payable[i],
                employee_payables: employee_payables[i],
                taxes_payable: taxes_payable[i],
                other_payables_total: other_payables_total[i],
                interest_payable: interest_payable[i],
                dividends_payable: dividends_payable[i],
                other_payables: other_payables[i],
                accrued_expenses: accrued_expenses[i],
                deferred_income_within_one_year: deferred_income_within_one_year[i],
                short_term_bonds_payable: short_term_bonds_payable[i],
                noncurrent_liabilities_due_within_one_year:
                    noncurrent_liabilities_due_within_one_year[i],
                other_current_liabilities: other_current_liabilities[i],
                total_current_liabilities: total_current_liabilities[i],
                long_term_loans: long_term_loans[i],
                bonds_payable: bonds_payable[i],
                lease_liabilities: lease_liabilities[i],
                long_term_employee_payables: long_term_employee_payables[i],
                long_term_payables_total: long_term_payables_total[i],
                long_term_payables: long_term_payables[i],
                special_payables: special_payables[i],
                estimated_noncurrent_liabilities: estimated_noncurrent_liabilities[i],
                deferred_income_tax_liabilities: deferred_income_tax_liabilities[i],
                long_term_deferred_income: long_term_deferred_income[i],
                other_noncurrent_liabilities: other_noncurrent_liabilities[i],
                total_noncurrent_liabilities: total_noncurrent_liabilities[i],
                total_liabilities: total_liabilities[i],
                paid_in_capital: paid_in_capital[i],
                capital_reserve: capital_reserve[i],
                treasury_stock: treasury_stock[i],
                other_comprehensive_income: other_comprehensive_income[i],
                special_reserve: special_reserve[i],
                surplus_reserve: surplus_reserve[i],
                general_risk_reserve: general_risk_reserve[i],
                undistributed_profits: undistributed_profits[i],
                total_equity_attributable_to_parent: total_equity_attributable_to_parent[i],
                minority_interest: minority_interest[i],
                total_equity: total_equity[i],
                total_liabilities_and_equity: total_liabilities_and_equity[i],
            };
            result.push(sheet);
        }
        Ok(result)
    }
}

use anyhow::Context;
use chrono::NaiveDate;
use derive_getters::Getters;
use reqwest::Client;

use crate::data_source::code_to_symbol::code_to_symbol;

pub enum AdjustDirection {
    Forward,
    Backword,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Get<PERSON>)]
pub struct SharePrice {
    date: NaiveDate,
    price: f32,
}

pub async fn get_adjusted_share_price(
    code: impl AsRef<str>,
    direction: AdjustDirection,
) -> anyhow::Result<Vec<SharePrice>> {
    let direction = match direction {
        AdjustDirection::Backword => "houfuquan",
        AdjustDirection::Forward => "qianfuquan",
    };
    let symbol = code_to_symbol(code.as_ref())
        .with_context(|| format!("failed to find symbol for code {}", code.as_ref()))?;

    let url = format!(
        "http://finance.sina.com.cn/realstock/company/{}/{}.js",
        symbol, direction
    );

    let response = Client::new().get(url).send().await?.text().await?;

    let data = json5::from_str::<serde_json::Value>(&response)?;
    let data = data
        .pointer("/0/data")
        .and_then(|x| x.as_object())
        .context("failed to parse data")?;

    let mut result: Vec<SharePrice> = Vec::with_capacity(data.len());

    for (key, value) in data.into_iter() {
        let date = NaiveDate::parse_from_str(key, "_%Y_%m_%d")?;
        let price = value
            .as_str()
            .and_then(|x| x.parse::<f32>().ok())
            .context("failed to parse share price")?;
        result.push(SharePrice { date, price });
    }

    Ok(result)
}

#[tokio::test]
async fn test_adjusted_share_price() {
    let res = get_adjusted_share_price("sz002351", AdjustDirection::Forward)
        .await
        .unwrap();

    println!("{:#?}", res);
}
